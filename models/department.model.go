package models

type Department struct {
	BaseModel
	MinistryID string  `json:"ministry_id" gorm:"column:ministry_id;type:uuid"`
	NameTh     string  `json:"name_th" gorm:"column:name_th"`
	NameEn     *string `json:"name_en" gorm:"column:name_en"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Ministry *Ministry `json:"ministry,omitempty" gorm:"foreignKey:MinistryID;references:ID"`
}

func (Department) TableName() string {
	return "departments"
}
