package models

type TimesheetType string

const (
	TimesheetTypeProject  TimesheetType = "PROJECT"
	TimesheetTypeSga      TimesheetType = "SGA"
	TimesheetTypeLeave    TimesheetType = "LEAVE"
	TimesheetTypeInternal TimesheetType = "INTERNAL"
	TimesheetTypeExternal TimesheetType = "EXTERNAL"
	TimesheetTypeOt       TimesheetType = "OT"
)

type Timesheet struct {
	BaseModelHardDelete
	ProjectID   *string           `json:"project_id" gorm:"column:project_id"`
	ProjectName *string           `json:"project_name" gorm:"column:project_name"`
	ProjectCode *string           `json:"project_code" gorm:"column:project_code"`
	SgaID       *string           `json:"sga_id" gorm:"column:sga_id"`
	SgaName     *string           `json:"sga_name" gorm:"column:sga_name"`
	UserID      string            `json:"user_id" gorm:"column:user_id"`
	Timing      float64           `json:"timing" gorm:"column:timing"`
	Type        TimesheetType     `json:"type" gorm:"column:type"`
	LeaveType   *CheckinLeaveType `json:"leave_type" gorm:"column:leave_type"`
	Description *string           `json:"description" gorm:"column:description"`
	Date        string            `json:"date" gorm:"column:date;type:date"`

	// Relations
	User    *User    `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Sga     *Sga     `json:"sga,omitempty" gorm:"foreignKey:SgaID"`
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectCode;references:Code"`
}

func (Timesheet) TableName() string {
	return "timesheets"
}
