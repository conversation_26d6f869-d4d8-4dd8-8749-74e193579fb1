package auth

import (
	"net/http"
	"net/url"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/consts"
	"gitlab.finema.co/finema/finework/finework-api/helpers"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type AuthController struct {
}

func (m AuthController) Logout(c core.IHTTPContext) error {
	authSvc := services.NewAuthService(c)
	err := authSvc.Logout(c.GetUser().Data["token"])
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.<PERSON>ontent(http.StatusNoContent)
}

func (m AuthController) SlackCallback(c core.IHTTPContext) error {
	// Get the authorization code from query parameters
	code := c.QueryParam("code")
	state := c.QueryParam("state")
	errorParam := c.QueryParam("error")

	// Handle OAuth error
	if errorParam != "" {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "SLACK_OAUTH_ERROR",
			"message": "Slack OAuth authorization failed: " + errorParam,
		})
	}

	// Validate required parameters
	if code == "" {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "MISSING_AUTHORIZATION_CODE",
			"message": "Authorization code is required",
		})
	}

	authSvc := services.NewAuthService(c)
	payload := &services.SlackCallbackPayload{
		Code:       code,
		State:      state,
		IPAddress:  c.RealIP(),
		UserAgent:  c.Request().UserAgent(),
		DeviceInfo: c.Request().Header.Get("X-Device-Info"),
	}

	response, err := authSvc.SlackCallback(payload)
	if err != nil {
		return c.Redirect(http.StatusFound, c.ENV().String(consts.EnvFrontendURL)+"/api/auth/callback"+"?error="+err.Error())
	}

	return c.Redirect(http.StatusFound, c.ENV().String(consts.EnvFrontendURL)+"/api/auth/callback"+"?token="+response.Token)
}

func (m AuthController) SlackLogin(c core.IHTTPContext) error {
	baseURL, _ := url.Parse("https://slack.com/openid/connect/authorize")

	// Create query parameters
	params := url.Values{}
	params.Add("response_type", "code")
	params.Add("scope", "openid profile email")
	params.Add("client_id", c.ENV().String(consts.EnvSlackClientID))
	params.Add("state", helpers.GenerateRandomString(32))
	params.Add("redirect_uri", strings.Replace(c.ENV().String(consts.EnvAPIURL), "http://", "https://", 1)+"/auth/slack-callback")

	// Add parameters to URL
	baseURL.RawQuery = params.Encode()

	return c.Redirect(http.StatusFound, baseURL.String())
}
