package department

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewDepartmentHTTP(e *echo.Echo) {
	department := &DepartmentController{}
	e.GET("/departments", core.WithHTTPContext(department.Pagination), middleware.AuthMiddleware())
	e.GET("/departments/:id", core.WithHTTPContext(department.Find), middleware.AuthMiddleware())

	e.POST("/admin/departments", core.WithHTTPContext(department.Create), middleware.AuthMiddleware())
	e.PUT("/admin/departments/:id", core.WithHTTPContext(department.Update), middleware.AuthMiddleware())
	e.DELETE("/admin/departments/:id", core.WithHTTPContext(department.Delete), middleware.AuthMiddleware())
}
