package project

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectController struct {
}

func (m ProjectController) Pagination(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	res, ierr := projectSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectController) Find(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	project, err := projectSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.<PERSON><PERSON>tatus(), err.J<PERSON><PERSON>())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Create(c core.IHTTPContext) error {
	input := &requests.ProjectCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectCreatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, project)
}

func (m ProjectController) Update(c core.IHTTPContext) error {
	input := &requests.ProjectUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectUpdatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Delete(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	err := projectSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
