package sga

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewSgaHTTP(e *echo.Echo) {
	sga := &SgaController{}
	e.GET("/sgas", core.WithHTTPContext(sga.Pagination), middleware.AuthMiddleware())
	e.GET("/sgas/:id", core.WithHTTPContext(sga.Find), middleware.AuthMiddleware())
	e.POST("/sgas", core.WithHTTPContext(sga.Create), middleware.AuthMiddleware())
	e.PUT("/sgas/:id", core.WithHTTPContext(sga.Update), middleware.AuthMiddleware())
	e.DELETE("/sgas/:id", core.WithHTTPContext(sga.Delete), middleware.AuthMiddleware())
}
