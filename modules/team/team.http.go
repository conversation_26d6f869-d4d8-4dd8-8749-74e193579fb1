package team

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewTeamHTTP(e *echo.Echo) {
	team := &TeamController{}
	e.GET("/teams", core.WithHTTPContext(team.Pagination), middleware.AuthMiddleware())
	e.GET("/teams/:id", core.WithHTTPContext(team.Find), middleware.AuthMiddleware())
	e.GET("/teams/code/:code", core.WithHTTPContext(team.FindByCode), middleware.AuthMiddleware())
	e.POST("/teams", core.WithHTTPContext(team.Create), middleware.AuthMiddleware())
	e.PUT("/teams/:id", core.WithHTTPContext(team.Update), middleware.AuthMiddleware())
	e.DELETE("/teams/:id", core.WithHTTPContext(team.Delete), middleware.AuthMiddleware())
}
