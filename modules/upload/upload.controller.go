package upload

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type UploadController struct{}

func (m UploadController) Upload(c core.IHTTPContext) error {
	fileHeader, err := c.FormFile("file")
	if err != nil {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "FILE_REQUIRED",
			"message": "file is required",
		})
	}

	uploadSvc := services.NewUploadService(c)
	res, ierr := uploadSvc.UploadFile(fileHeader)
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

