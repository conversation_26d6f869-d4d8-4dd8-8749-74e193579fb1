{"info": {"_postman_id": "auth-module-collection", "name": "Finework API - Auth Module", "description": "Authentication endpoints for Finework API including Slack OAuth integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/slack-login", "host": ["{{baseUrl}}"], "path": ["auth", "slack-login"]}, "description": "Initiates Slack OAuth login flow. This will redirect to Slack's authorization page."}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/slack-callback?code={{slack_code}}&state={{slack_state}}", "host": ["{{baseUrl}}"], "path": ["auth", "slack-callback"], "query": [{"key": "code", "value": "{{slack_code}}", "description": "OAuth authorization code from Slack"}, {"key": "state", "value": "{{slack_state}}", "description": "OAuth state parameter for security"}]}, "description": "<PERSON>les Slack OAuth callback and returns authentication token"}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "description": "JWT authentication token"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}, "description": "Logs out the current user and invalidates the authentication token"}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "slack_code", "value": "SLACK_AUTH_CODE", "type": "string"}, {"key": "slack_state", "value": "SLACK_STATE_VALUE", "type": "string"}]}