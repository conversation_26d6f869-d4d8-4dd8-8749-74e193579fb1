{"info": {"_postman_id": "finework-api-complete", "name": "Finework API - Complete Collection", "description": "Complete Postman collection for all Finework API modules including Auth, Users, Teams, Holidays, Me, and Home endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Home", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "response": []}]}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/slack-login", "host": ["{{baseUrl}}"], "path": ["auth", "slack-login"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/slack-callback?code={{slack_code}}&state={{slack_state}}", "host": ["{{baseUrl}}"], "path": ["auth", "slack-callback"], "query": [{"key": "code", "value": "{{slack_code}}"}, {"key": "state", "value": "{{slack_state}}"}]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "response": []}]}, {"name": "Users", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "{{page}}"}, {"key": "limit", "value": "{{limit}}"}]}}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}"]}}, "response": []}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securepassword123\",\n  \"full_name\": \"<PERSON>\",\n  \"display_name\": \"<PERSON>\",\n  \"position\": \"Software Developer\",\n  \"team_code\": \"DEV\",\n  \"avatar_url\": \"https://example.com/avatar.jpg\",\n  \"role\": \"USER\"\n}"}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"<PERSON>\",\n  \"display_name\": \"<PERSON>\",\n  \"position\": \"Senior Software Developer\",\n  \"team_code\": \"DEV\",\n  \"avatar_url\": \"https://example.com/new-avatar.jpg\",\n  \"role\": \"ADMIN\"\n}"}, "url": {"raw": "{{baseUrl}}/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}"]}}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}"]}}, "response": []}]}, {"name": "Teams", "item": [{"name": "Get All Teams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["teams"], "query": [{"key": "page", "value": "{{page}}"}, {"key": "limit", "value": "{{limit}}"}]}}, "response": []}, {"name": "Get Team by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams/{{team_id}}", "host": ["{{baseUrl}}"], "path": ["teams", "{{team_id}}"]}}, "response": []}, {"name": "Get Team by Code", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams/code/{{team_code}}", "host": ["{{baseUrl}}"], "path": ["teams", "code", "{{team_code}}"]}}, "response": []}, {"name": "Create Team", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Development Team\",\n  \"code\": \"DEV\",\n  \"description\": \"Software development team\",\n  \"working_start_at\": \"2024-01-01T09:00:00Z\",\n  \"working_end_at\": \"2024-01-01T18:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/teams", "host": ["{{baseUrl}}"], "path": ["teams"]}}, "response": []}, {"name": "Update Team", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Development Team Updated\",\n  \"code\": \"DEV\",\n  \"description\": \"Updated software development team\",\n  \"working_start_at\": \"2024-01-01T08:30:00Z\",\n  \"working_end_at\": \"2024-01-01T17:30:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/teams/{{team_id}}", "host": ["{{baseUrl}}"], "path": ["teams", "{{team_id}}"]}}, "response": []}, {"name": "Delete Team", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams/{{team_id}}", "host": ["{{baseUrl}}"], "path": ["teams", "{{team_id}}"]}}, "response": []}]}, {"name": "Holidays", "item": [{"name": "Get All Holidays", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/holidays?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["holidays"], "query": [{"key": "page", "value": "{{page}}"}, {"key": "limit", "value": "{{limit}}"}]}}, "response": []}, {"name": "Get Holiday by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/holidays/{{holiday_id}}", "host": ["{{baseUrl}}"], "path": ["holidays", "{{holiday_id}}"]}}, "response": []}, {"name": "Create Holiday", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Year's Day\",\n  \"date\": \"2024-01-01T00:00:00Z\",\n  \"is_national\": true\n}"}, "url": {"raw": "{{baseUrl}}/holidays", "host": ["{{baseUrl}}"], "path": ["holidays"]}}, "response": []}, {"name": "Update Holiday", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Year's Day (Updated)\",\n  \"date\": \"2024-01-01T00:00:00Z\",\n  \"is_national\": false\n}"}, "url": {"raw": "{{baseUrl}}/holidays/{{holiday_id}}", "host": ["{{baseUrl}}"], "path": ["holidays", "{{holiday_id}}"]}}, "response": []}, {"name": "Delete Holiday", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/holidays/{{holiday_id}}", "host": ["{{baseUrl}}"], "path": ["holidays", "{{holiday_id}}"]}}, "response": []}]}, {"name": "Me (Profile)", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/me", "host": ["{{baseUrl}}"], "path": ["me"]}}, "response": []}, {"name": "Update My Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"Updated Name\",\n  \"display_name\": \"Updated Display\",\n  \"position\": \"Updated Position\",\n  \"team_code\": \"DEV\",\n  \"avatar_url\": \"https://example.com/updated-avatar.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/me", "host": ["{{baseUrl}}"], "path": ["me"]}}, "response": []}, {"name": "Get My Devices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/me/devices?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["me", "devices"], "query": [{"key": "page", "value": "{{page}}"}, {"key": "limit", "value": "{{limit}}"}]}}, "response": []}, {"name": "Delete My Device", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/me/devices/{{device_id}}", "host": ["{{baseUrl}}"], "path": ["me", "devices", "{{device_id}}"]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "page", "value": "1", "type": "string"}, {"key": "limit", "value": "10", "type": "string"}, {"key": "user_id", "value": "USER_ID_HERE", "type": "string"}, {"key": "team_id", "value": "TEAM_ID_HERE", "type": "string"}, {"key": "team_code", "value": "DEV", "type": "string"}, {"key": "holiday_id", "value": "HOLIDAY_ID_HERE", "type": "string"}, {"key": "device_id", "value": "DEVICE_ID_HERE", "type": "string"}, {"key": "slack_code", "value": "SLACK_AUTH_CODE", "type": "string"}, {"key": "slack_state", "value": "SLACK_STATE_VALUE", "type": "string"}]}