{"id": "finework-api-environment", "name": "Finework API Environment", "values": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "token", "value": "", "type": "secret", "enabled": true}, {"key": "page", "value": "1", "type": "default", "enabled": true}, {"key": "limit", "value": "10", "type": "default", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "team_id", "value": "", "type": "default", "enabled": true}, {"key": "team_code", "value": "DEV", "type": "default", "enabled": true}, {"key": "holiday_id", "value": "", "type": "default", "enabled": true}, {"key": "device_id", "value": "", "type": "default", "enabled": true}, {"key": "slack_code", "value": "", "type": "default", "enabled": true}, {"key": "slack_state", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-07T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}