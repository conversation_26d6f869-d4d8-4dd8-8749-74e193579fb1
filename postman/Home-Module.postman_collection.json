{"info": {"_postman_id": "home-module-collection", "name": "Finework API - Home Module", "description": "Basic health check endpoint for Finework API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}, "description": "Basic health check endpoint to verify API connectivity. Returns status: 'i'm ok'"}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}