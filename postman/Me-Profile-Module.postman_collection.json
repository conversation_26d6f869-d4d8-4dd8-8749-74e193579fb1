{"info": {"_postman_id": "me-profile-module-collection", "name": "Finework API - Me (Profile) Module", "description": "Profile management endpoints for current authenticated user", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/me", "host": ["{{baseUrl}}"], "path": ["me"]}, "description": "Get the current authenticated user's profile information"}, "response": []}, {"name": "Update My Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"Updated Name\",\n  \"display_name\": \"Updated Display\",\n  \"position\": \"Updated Position\",\n  \"team_code\": \"DEV\",\n  \"avatar_url\": \"https://example.com/updated-avatar.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me", "host": ["{{baseUrl}}"], "path": ["me"]}, "description": "Update the current authenticated user's profile. All fields are optional. Note: Users cannot change their own role through this endpoint."}, "response": []}, {"name": "Get My Devices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/me/devices?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["me", "devices"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number for pagination"}, {"key": "limit", "value": "{{limit}}", "description": "Number of items per page"}]}, "description": "Get paginated list of current user's active devices/tokens with metadata like IP address, user agent, and device info"}, "response": []}, {"name": "Delete My Device", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/me/devices/{{device_id}}", "host": ["{{baseUrl}}"], "path": ["me", "devices", "{{device_id}}"]}, "description": "Delete/revoke a specific device/token. This will log out that specific device/session."}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "page", "value": "1", "type": "string"}, {"key": "limit", "value": "10", "type": "string"}, {"key": "device_id", "value": "DEVICE_ID_HERE", "type": "string"}]}