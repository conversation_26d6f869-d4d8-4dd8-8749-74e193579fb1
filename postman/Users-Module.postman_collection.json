{"info": {"_postman_id": "users-module-collection", "name": "Finework API - Users Module", "description": "User management endpoints for Finework API with full CRUD operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number for pagination"}, {"key": "limit", "value": "{{limit}}", "description": "Number of items per page"}]}, "description": "Retrieve paginated list of all users"}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}"]}, "description": "Retrieve a specific user by their ID"}, "response": []}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securepassword123\",\n  \"full_name\": \"<PERSON>\",\n  \"display_name\": \"<PERSON>\",\n  \"position\": \"Software Developer\",\n  \"team_code\": \"DEV\",\n  \"avatar_url\": \"https://example.com/avatar.jpg\",\n  \"role\": \"USER\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}, "description": "Create a new user. Required fields: email, password, full_name. Valid roles: USER, ADMIN, SUPER_ADMIN"}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"<PERSON>\",\n  \"display_name\": \"<PERSON>\",\n  \"position\": \"Senior Software Developer\",\n  \"team_code\": \"DEV\",\n  \"avatar_url\": \"https://example.com/new-avatar.jpg\",\n  \"role\": \"ADMIN\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}"]}, "description": "Update an existing user. All fields are optional for updates."}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}"]}, "description": "Delete a user by their ID"}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "page", "value": "1", "type": "string"}, {"key": "limit", "value": "10", "type": "string"}, {"key": "user_id", "value": "USER_ID_HERE", "type": "string"}]}