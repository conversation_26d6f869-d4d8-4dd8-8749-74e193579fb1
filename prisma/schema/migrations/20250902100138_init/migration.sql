-- Create<PERSON>num
CREATE TYPE "public"."CheckinType" AS ENUM ('OFFICE_HQ', 'OFFICE_AKV', 'WFH', 'ONSITE', 'BUSINESS_TRIP', 'LEAVE');

-- CreateEnum
CREATE TYPE "public"."CheckinPeriod" AS ENUM ('FULL_DAY', 'HALF_MORNING', 'HALF_AFTERNOON', 'MANY_DAYS');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."CheckinLeaveType" AS ENUM ('ANNUAL', 'SICK', 'BUSINESS', 'MENSTRUAL', 'BIRTHDAY', 'ORDINATION');

-- Create<PERSON>num
CREATE TYPE "public"."TimesheetType" AS ENUM ('PROJECT', 'SGA', 'LEAVE', 'INTERNAL', 'EXTERNAL', 'OT');

-- CreateEnum
CREATE TYPE "public"."PermissionLevel" AS ENUM ('NONE', 'USER', 'ADMIN', 'SUPER');

-- CreateTable
CREATE TABLE "public"."checkins" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "type" "public"."CheckinType" NOT NULL,
    "leave_type" "public"."CheckinLeaveType",
    "period" "public"."CheckinPeriod" NOT NULL,
    "location" TEXT NOT NULL,
    "remarks" TEXT,
    "is_unused" BOOLEAN NOT NULL DEFAULT false,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "first_checkin_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "checkins_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."departments" (
    "id" UUID NOT NULL,
    "ministry_id" UUID NOT NULL,
    "name_th" TEXT NOT NULL,
    "name_en" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "departments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."holidays" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,

    CONSTRAINT "holidays_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ministries" (
    "id" UUID NOT NULL,
    "name_th" TEXT NOT NULL,
    "name_en" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "ministries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."projects" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "projects_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."sgas" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "sgas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."teams" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "color" TEXT NOT NULL DEFAULT '',
    "working_start_at" TIME,
    "working_end_at" TIME,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "teams_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."timesheets" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "sga_id" UUID,
    "sga_name" TEXT,
    "project_id" UUID,
    "project_code" TEXT,
    "project_name" TEXT,
    "timing" DOUBLE PRECISION NOT NULL,
    "type" "public"."TimesheetType" NOT NULL,
    "leave_type" "public"."CheckinLeaveType",
    "description" TEXT,
    "date" DATE NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "timesheets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."user_access_levels" (
    "user_id" UUID NOT NULL,
    "clockin" "public"."PermissionLevel" NOT NULL DEFAULT 'USER',
    "timesheet" "public"."PermissionLevel" NOT NULL DEFAULT 'USER',
    "pmo" "public"."PermissionLevel" NOT NULL DEFAULT 'NONE',
    "setting" "public"."PermissionLevel" NOT NULL DEFAULT 'NONE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_access_levels_pkey" PRIMARY KEY ("user_id")
);

-- CreateTable
CREATE TABLE "public"."user_tokens" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "token" TEXT NOT NULL,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "device_info" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."users" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "full_name" TEXT,
    "display_name" TEXT,
    "position" TEXT,
    "team_code" TEXT,
    "avatar_url" TEXT,
    "company" TEXT,
    "slack_id" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "joined_date" DATE,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "checkins_type_location_idx" ON "public"."checkins"("type", "location");

-- CreateIndex
CREATE UNIQUE INDEX "departments_name_th_key" ON "public"."departments"("name_th");

-- CreateIndex
CREATE UNIQUE INDEX "departments_name_en_key" ON "public"."departments"("name_en");

-- CreateIndex
CREATE INDEX "departments_ministry_id_idx" ON "public"."departments"("ministry_id");

-- CreateIndex
CREATE INDEX "departments_name_th_idx" ON "public"."departments"("name_th");

-- CreateIndex
CREATE INDEX "holidays_name_date_idx" ON "public"."holidays"("name", "date");

-- CreateIndex
CREATE UNIQUE INDEX "ministries_name_th_key" ON "public"."ministries"("name_th");

-- CreateIndex
CREATE UNIQUE INDEX "ministries_name_en_key" ON "public"."ministries"("name_en");

-- CreateIndex
CREATE INDEX "ministries_name_th_idx" ON "public"."ministries"("name_th");

-- CreateIndex
CREATE UNIQUE INDEX "projects_name_key" ON "public"."projects"("name");

-- CreateIndex
CREATE UNIQUE INDEX "projects_code_key" ON "public"."projects"("code");

-- CreateIndex
CREATE INDEX "projects_name_code_idx" ON "public"."projects"("name", "code");

-- CreateIndex
CREATE UNIQUE INDEX "sgas_name_key" ON "public"."sgas"("name");

-- CreateIndex
CREATE INDEX "sgas_name_idx" ON "public"."sgas"("name");

-- CreateIndex
CREATE UNIQUE INDEX "teams_name_key" ON "public"."teams"("name");

-- CreateIndex
CREATE UNIQUE INDEX "teams_code_key" ON "public"."teams"("code");

-- CreateIndex
CREATE INDEX "teams_name_code_idx" ON "public"."teams"("name", "code");

-- CreateIndex
CREATE INDEX "timesheets_type_idx" ON "public"."timesheets"("type");

-- CreateIndex
CREATE UNIQUE INDEX "user_tokens_token_key" ON "public"."user_tokens"("token");

-- CreateIndex
CREATE INDEX "user_tokens_user_id_idx" ON "public"."user_tokens"("user_id");

-- CreateIndex
CREATE INDEX "user_tokens_token_idx" ON "public"."user_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_slack_id_key" ON "public"."users"("slack_id");

-- CreateIndex
CREATE INDEX "users_email_team_code_idx" ON "public"."users"("email", "team_code");

-- AddForeignKey
ALTER TABLE "public"."checkins" ADD CONSTRAINT "checkins_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."departments" ADD CONSTRAINT "departments_ministry_id_fkey" FOREIGN KEY ("ministry_id") REFERENCES "public"."ministries"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."timesheets" ADD CONSTRAINT "timesheets_project_code_fkey" FOREIGN KEY ("project_code") REFERENCES "public"."projects"("code") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."timesheets" ADD CONSTRAINT "timesheets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."timesheets" ADD CONSTRAINT "timesheets_sga_id_fkey" FOREIGN KEY ("sga_id") REFERENCES "public"."sgas"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_access_levels" ADD CONSTRAINT "user_access_levels_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_tokens" ADD CONSTRAINT "user_tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."users" ADD CONSTRAINT "users_team_code_fkey" FOREIGN KEY ("team_code") REFERENCES "public"."teams"("code") ON DELETE SET NULL ON UPDATE CASCADE;
