model projects {
  id          String  @id @default(uuid()) @db.Uuid
  name        String  @unique
  code        String  @unique
  description String?

  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  timesheets timesheets[]

  @@index([name, code])
}
