enum PermissionLevel {
  NONE
  USER
  ADMIN
  SUPER
}

model user_access_levels {
  user_id    String          @id @db.Uuid
  clockin    PermissionLevel @default(USER)
  timesheet  PermissionLevel @default(USER)
  pmo        PermissionLevel @default(NONE)
  setting    PermissionLevel @default(NONE)
  created_at DateTime        @default(now())
  updated_at DateTime        @default(now()) @updatedAt

  // Relations
  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)
}
