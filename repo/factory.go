package repo

import (
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// Option is a generic repository option that can mutate the repository query.
// Example usage:
//
//	type UserOption = Option[models.User]
//	repo := Build[models.User](ctx, someUserOption)
type Option[T repository.IModel] func(repository.IRepository[T])

// Build creates a repository for the given model type and applies provided options.
func Build[T repository.IModel](c core.IContext, options ...Option[T]) repository.IRepository[T] {
	r := repository.New[T](c)
	for _, opt := range options {
		opt(r)
	}
	return r
}

// Factory is a typed constructor for repositories of model T.
// It enables concise declarations like:
//
//	var User = Make[models.User]()
//	// usage: repo := User(ctx, UserWithSearch("john"))
type Factory[T repository.IModel] func(core.IContext, ...Option[T]) repository.IRepository[T]

// Make returns a repository factory function for model T that applies options.
func Make[T repository.IModel]() Factory[T] {
	return func(c core.IContext, options ...Option[T]) repository.IRepository[T] {
		return Build[T](c, options...)
	}
}
