package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type SgaOption = Option[models.Sga]

var Sga = Make[models.Sga]()

func SgaOrderBy(pageOptions *core.PageOptions) SgaOption {
	return func(c repository.IRepository[models.Sga]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func SgaWithSearch(q string) SgaOption {
	return func(c repository.IRepository[models.Sga]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ?", searchTerm)
	}
}
