package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type UserTokenOption = Option[models.UserToken]

var UserToken = Make[models.UserToken]()

func UserTokenOrderBy(pageOptions *core.PageOptions) UserTokenOption {
	return func(c repository.IRepository[models.UserToken]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}
