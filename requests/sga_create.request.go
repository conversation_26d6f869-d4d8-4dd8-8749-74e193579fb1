package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type SgaCreate struct {
	core.BaseValidator
	Name        *string `json:"name"`
	Description *string `json:"description"`
}

func (r *SgaCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>(ctx, r.Name, models.Sga{}.TableName(), "name", "", "name"))

	return r.Error()
}
