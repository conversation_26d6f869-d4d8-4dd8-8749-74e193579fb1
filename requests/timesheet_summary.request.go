package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type TimesheetSummaryReportRequest struct {
	core.BaseValidator
	StartDate *string `json:"start_date" query:"start_date"`
	EndDate   *string `json:"end_date" query:"end_date"`
	TeamCode  *string `json:"team" query:"team_code"`
}

func (r *TimesheetSummaryReportRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))

	if r.TeamCode != nil {
		r.Must(r.IsExists(ctx, r.TeamCode, models.Team{}.TableName(), "code", "team"))
	}

	return r.Error()
}
