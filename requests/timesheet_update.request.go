package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetUpdate struct {
	core.BaseValidator
	ProjectCode *string  `json:"project_code"`
	SgaID       *string  `json:"sga_id"`
	Timing      *float64 `json:"timing"`
	Type        *string  `json:"type"`
	LeaveType   *string  `json:"leave_type"`
	Description *string  `json:"description"`
	Date        *string  `json:"date"`

	// Relations
	ProjectID   *string `json:"project_id"`
	ProjectName *string `json:"project_name"`
	SgaName     *string `json:"sga_name"`
}

func (r *TimesheetUpdate) Valid(ctx core.IContext) core.IError {
	if r.Must(r.<PERSON>equired(r.Type, "type")) {
		switch utils.ToNonPointer(r.Type) {
		case string(models.TimesheetTypeLeave):
			r.Must(r.IsStrRequired(r.LeaveType, "leave_type"))
			r.Must(r.IsStrIn(r.LeaveType, strings.Join([]string{
				string(models.CheckinLeaveTypeAnnual),
				string(models.CheckinLeaveTypeSick),
				string(models.CheckinLeaveTypeBusiness),
				string(models.CheckinLeaveTypeMenstrual),
				string(models.CheckinLeaveTypeBirthday),
				string(models.CheckinLeaveTypeOrdination),
			}, "|"), "leave_type"))
		case string(models.TimesheetTypeProject):
			r.Must(r.IsStrRequired(r.ProjectCode, "project_code"))
		case string(models.TimesheetTypeSga):
			r.Must(r.IsStrRequired(r.SgaID, "sga_id"))
		}
	}
	r.Must(r.IsRequired(r.Date, "date"))
	r.Must(r.IsRequired(r.Timing, "timing"))
	r.Must(r.IsStrIn(r.Type, strings.Join([]string{string(models.TimesheetTypeProject), string(models.TimesheetTypeSga),
		string(models.TimesheetTypeLeave), string(models.TimesheetTypeInternal),
		string(models.TimesheetTypeExternal), string(models.TimesheetTypeOt)}, "|"), "type"))

	if r.ProjectCode != nil {
		project, _ := repo.Project(ctx).FindOne("code = ?", r.ProjectCode)
		if project != nil {
			r.ProjectID = &project.ID
			r.ProjectName = &project.Name
		}
	}

	if r.SgaID != nil {
		sga, _ := repo.Sga(ctx).FindOne("id = ?", r.SgaID)
		if sga != nil {
			r.SgaName = &sga.Name
		}
	}

	return r.Error()
}
