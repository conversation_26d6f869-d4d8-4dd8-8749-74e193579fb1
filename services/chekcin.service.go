package services

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ICheckinService interface {
	Create(input *CheckinCreatePayload) ([]models.Checkin, core.IError)
	Update(id string, input *CheckinUpdatePayload) (*models.Checkin, core.IError)
	Find(id string) (*models.Checkin, core.IError)
	Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError)
	Delete(id string) core.IError
}

type checkinService struct {
	ctx core.IContext
}

func (s checkinService) Create(input *CheckinCreatePayload) ([]models.Checkin, core.IError) {
	checkins := []models.Checkin{}
	for _, item := range input.Items {
		checkin := models.Checkin{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			UserId:              input.UserId,
			Type:                models.CheckinType(item.Type),
			Period:              models.CheckinPeriod(item.Period),
			Location:            item.Location,
			Remarks:             item.Remarks,
			IsUnused:            item.IsUnused,
			Date:                item.Date,
		}
		checkins = append(checkins, checkin)
	}

	ierr := s.updateUnusedByUserAndDate(input.UserId, input.Items[0].Date)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	ierr = repo.Checkin(s.ctx).Create(checkins)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return checkins, nil
}

func (s checkinService) Update(id string, input *CheckinUpdatePayload) (*models.Checkin, core.IError) {
	checkin, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if input.Period != "" {
		checkin.Period = models.CheckinPeriod(input.Period)
	}
	if input.Location != nil {
		checkin.Location = input.Location
	}
	if input.Remarks != nil {
		checkin.Remarks = input.Remarks
	}
	if input.IsUnused != nil {
		checkin.IsUnused = input.IsUnused
	}
	if input.Type != "" {
		checkin.Type = models.CheckinType(input.Type)
	}
	if input.Date != nil {
		checkin.Date = input.Date
	}

	// Update the checkin in the repository
	ierr = repo.Checkin(s.ctx).Where("id = ?", id).Updates(checkin)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(checkin.ID)
}

func (s checkinService) Find(id string) (*models.Checkin, core.IError) {
	return repo.Checkin(s.ctx, repo.CheckinWithAllRelation()).FindOne("id = ?", id)
}

func (s checkinService) Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError) {
	return repo.Checkin(
		s.ctx,
		repo.CheckinWithAllRelation(),
		repo.CheckinWithUser(options.UserID),
		repo.CheckinWithDateRange(options.StartDate, options.EndDate),
		repo.CheckinWithTeamCode(options.TeamCode),
		repo.CheckinWithType(options.Type),
		repo.CheckinOrderBy(pageOptions)).
		Pagination(pageOptions)
}

func (s checkinService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Checkin(s.ctx).Delete("id = ?", id)
}

// Helper method to find existing checkin by user ID and date
func (s checkinService) updateUnusedByUserAndDate(userId string, date *time.Time) core.IError {
	if date == nil {
		return nil
	}

	// Format date to compare only the date part (without time)
	dateStr := date.Format("2006-01-02")
	return repo.Checkin(s.ctx).Where("user_id = ? AND DATE(date) = ?", userId, dateStr).Updates(map[string]interface{}{
		"is_unused": true,
	})
}

func NewCheckinService(ctx core.IContext) ICheckinService {
	return &checkinService{ctx: ctx}
}
