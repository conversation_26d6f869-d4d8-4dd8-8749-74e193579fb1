### Test CRUD Operations for Ministries and Departments

### Variables
@baseUrl = http://localhost:3000
@token = YOUR_AUTH_TOKEN_HERE

### ===== MINISTRIES CRUD =====

### Create Ministry
POST {{baseUrl}}/ministries
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name_th": "กระทรวงการคลัง",
  "name_en": "Ministry of Finance"
}

### Get All Ministries (Pagination)
GET {{baseUrl}}/ministries
Authorization: Bearer {{token}}

### Get Ministry by ID
GET {{baseUrl}}/ministries/MINISTRY_ID_HERE
Authorization: Bearer {{token}}

### Update Ministry
PUT {{baseUrl}}/ministries/MINISTRY_ID_HERE
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name_th": "กระทรวงการคลัง (อัปเดต)",
  "name_en": "Ministry of Finance (Updated)"
}

### Delete Ministry
DELETE {{baseUrl}}/ministries/MINISTRY_ID_HERE
Authorization: Bearer {{token}}

### ===== DEPARTMENTS CRUD =====

### Create Department
POST {{baseUrl}}/departments
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "ministry_id": "MINISTRY_ID_HERE",
  "name_th": "กรมบัญชีกลาง",
  "name_en": "Comptroller General's Department"
}

### Get All Departments (Pagination)
GET {{baseUrl}}/departments
Authorization: Bearer {{token}}

### Get Department by ID
GET {{baseUrl}}/departments/DEPARTMENT_ID_HERE
Authorization: Bearer {{token}}

### Update Department
PUT {{baseUrl}}/departments/DEPARTMENT_ID_HERE
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "ministry_id": "MINISTRY_ID_HERE",
  "name_th": "กรมบัญชีกลาง (อัปเดต)",
  "name_en": "Comptroller General's Department (Updated)"
}

### Delete Department
DELETE {{baseUrl}}/departments/DEPARTMENT_ID_HERE
Authorization: Bearer {{token}}

### ===== EXAMPLE WORKFLOW =====

### 1. Create a Ministry first
POST {{baseUrl}}/ministries
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name_th": "กระทรวงศึกษาธิการ",
  "name_en": "Ministry of Education"
}

### 2. Then create a Department under that Ministry
POST {{baseUrl}}/departments
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "ministry_id": "REPLACE_WITH_MINISTRY_ID_FROM_STEP_1",
  "name_th": "กรมการศึกษาขั้นพื้นฐาน",
  "name_en": "Department of Basic Education"
}
